'use client';

import { Caption } from '@telegram-apps/telegram-ui';
import { useState } from 'react';
import { toast } from 'sonner';

import { makeSecondaryMarketPurchase } from '@/api/orders-api';
import { SellPriceDetails } from '@/components/shared/sell-price-details';
import { TonLogo } from '@/components/TonLogo';
import { Button } from '@/components/ui/button';
import { ResellTxHistory } from '@/components/ui/order/resell-tx-history';
import { OrderActors } from '@/components/ui/order-actors';
import { type OrderEntity, UserType } from '@/constants/core.constants';
import { useRootContext } from '@/root-context';
import { executeMarketplaceOrderAction } from '@/utils/order-action-utils';
import { isSecondaryMarketOrder } from '@/utils/secondary-market-utils';

import { UserOrderPaymentDetailsSection } from '../orders/user-order-details-drawer/user-order-payment-details-section';
import {
  OrderDetailsActionButtons,
  OrderDetailsBaseDrawer,
  OrderDetailsHeaderSection,
  OrderDetailsImageSection,
} from './order-details-drawer/index';

interface OrderDetailsDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  userType?: UserType;
  onOrderAction?: () => void;
  hideActionButton?: boolean; // For activity page where we don't want to show buy button
}

export function OrderDetailsDrawer({
  open,
  onOpenChange,
  order,
  userType,
  onOrderAction,
  hideActionButton = false,
}: OrderDetailsDrawerProps) {
  const { collections, currentUser } = useRootContext();
  const [actionLoading, setActionLoading] = useState(false);
  const [showResellHistory, setShowResellHistory] = useState(false);

  const collection = order
    ? collections.find((c) => c.id === order.collectionId) || null
    : null;

  const isSecondary = order ? isSecondaryMarketOrder(order) : false;
  const effectiveUserType = userType || UserType.BUYER;

  const handleAction = async () => {
    if (!order?.id) return;

    setActionLoading(true);

    let result;
    if (isSecondary) {
      try {
        const purchaseResult = await makeSecondaryMarketPurchase(order.id);
        result = {
          success: purchaseResult.success,
          message: purchaseResult.message,
        };
        if (purchaseResult.success) {
          toast.success(
            purchaseResult.message ||
              'Secondary market purchase completed successfully!',
          );
        } else {
          toast.error(
            purchaseResult.message || 'Secondary market purchase failed',
          );
        }
      } catch (error) {
        console.error('Error making secondary market purchase:', error);
        const errorMessage =
          error instanceof Error
            ? error.message
            : 'Secondary market purchase failed. Please try again.';
        toast.error(errorMessage);
        result = { success: false, message: errorMessage };
      }
    } else {
      result = await executeMarketplaceOrderAction(order.id, effectiveUserType);
    }

    if (result.success) {
      onOpenChange(false);
      if (onOrderAction) {
        onOrderAction();
      }
    }
    setActionLoading(false);
  };

  const handleDrawerClose = () => {
    onOpenChange(false);
  };

  const handleShowResellHistory = () => {
    setShowResellHistory(true);
  };

  // TODO: Temporarily hidden - resell tx history feature
  const shouldShowResellHistory = false;
  // order?.secondaryMarketPrice && order.secondaryMarketPrice > 0;

  if (!order) return null;

  // Check if current user is the reseller (buyerId) of a secondary market order
  const isCurrentUserReseller =
    isSecondary && currentUser?.id === order.buyerId;

  // For activity orders, only show buy button for secondary market orders
  // Also hide buy button if current user is the reseller
  const shouldHideActionButton =
    (hideActionButton && !isSecondary) || isCurrentUserReseller;

  const actionLabel = (
    <>
      {isSecondary ? (
        <div className="text-lg flex items-center gap-1">
          <span>
            Buy <span className=" font-bold">{order.secondaryMarketPrice}</span>
          </span>
          <TonLogo className="-ml-[5px]" size={24} />
        </div>
      ) : (
        <div className="text-lg  flex items-center gap-1">
          <span>
            {effectiveUserType === UserType.BUYER ? 'Fulfill' : 'Buy'}{' '}
            <span className="font-bold">{order.price}</span>
          </span>
          <TonLogo className="-ml-[5px]" size={24} />
        </div>
      )}
    </>
  );

  return (
    <OrderDetailsBaseDrawer open={open} onOpenChange={onOpenChange}>
      <OrderDetailsImageSection
        collectionId={order.collectionId}
        collection={collection}
      />

      <OrderDetailsHeaderSection
        {...{
          order,
          collection,
        }}
      />

      <SellPriceDetails order={order} className="py-4" />

      {collection?.description && (
        <div className="text-center">
          <Caption level="2" weight="3" className="text-[#708499]">
            {collection.description}
          </Caption>
        </div>
      )}

      <UserOrderPaymentDetailsSection order={order} />

      <OrderActors
        buyerId={order.buyerId}
        sellerId={order.sellerId}
        isOpen={open}
      />

      {!shouldHideActionButton ? (
        <OrderDetailsActionButtons
          primaryAction={{
            label: actionLabel,
            onClick: handleAction,
            loading: actionLoading,
          }}
          secondaryAction={
            shouldShowResellHistory
              ? {
                  label: 'Show Resell History',
                  onClick: handleShowResellHistory,
                }
              : undefined
          }
          onClose={handleDrawerClose}
          actionLoading={actionLoading}
        />
      ) : (
        <div className="space-y-3 pt-4">
          <Button
            variant="outline"
            onClick={handleDrawerClose}
            className="w-full h-12 border-[#3a4a5c] text-[#f5f5f5] hover:bg-[#232e3c]/50 bg-transparent rounded-2xl"
          >
            Close
          </Button>
        </div>
      )}

      {showResellHistory && order && (
        <div className="fixed inset-0 bg-black/50 z-[60] flex items-center justify-center p-4">
          <div className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <ResellTxHistory
              order={order}
              onClose={() => setShowResellHistory(false)}
            />
          </div>
        </div>
      )}
    </OrderDetailsBaseDrawer>
  );
}

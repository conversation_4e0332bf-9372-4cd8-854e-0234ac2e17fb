import { CollectionEntity, AppConfigEntity } from "../types";

export const DEFAULT_LOCK_PERIOD_DAYS = 21;

export function getEffectiveLockPeriod(
  collection: CollectionEntity | null,
  appConfig: AppConfigEntity | null,
): number {
  // Priority: collection.lock_period -> appConfig.lock_period -> fallback constant
  if (collection?.lock_period && collection.lock_period > 0) {
    return collection.lock_period;
  }
  if (appConfig?.lock_period && appConfig.lock_period > 0) {
    return appConfig.lock_period;
  }
  return DEFAULT_LOCK_PERIOD_DAYS; // 21 days fallback
}

export function calculateLockPeriodMs(
  collection: CollectionEntity | null,
  appConfig: AppConfigEntity | null,
): number {
  const lockPeriodDays = getEffectiveLockPeriod(collection, appConfig);
  return lockPeriodDays * 24 * 60 * 60 * 1000;
}

export function isInLockPeriod(
  collection: CollectionEntity | null,
  appConfig: AppConfigEntity | null = null,
): boolean {
  if (!collection?.launchedAt) return false;

  const now = new Date();
  const launchedAt = collection.launchedAt.toDate();
  const lockPeriodMs = calculateLockPeriodMs(collection, appConfig);
  const lockEndDate = new Date(launchedAt.getTime() + lockPeriodMs);

  return now < lockEndDate;
}

export function getLockEndDate(
  collection: CollectionEntity | null,
  appConfig: AppConfigEntity | null = null,
): Date | null {
  if (!collection?.launchedAt) return null;

  const launchedAt = collection.launchedAt.toDate();
  const lockPeriodMs = calculateLockPeriodMs(collection, appConfig);
  return new Date(launchedAt.getTime() + lockPeriodMs);
}
